import asyncio
import copy
import logging
from datetime import UTC, datetime
from typing import Any

import utils_general
import utils_oracle
from datagrabber import CatalogItem, get_instruments_async
from utils_oracle import Network

from .config import EXPIRED_INSTRUMENTS_TIMEDELTA
from .typings import (
    AssetTypeToCurrencyConstantTenors,
    BaseFeedMetaData,
    CatalogItemsMap,
    ChainCatalogFilters,
    ChainConstantTenorInfo,
    FeedIndexerCatalogFilter,
    Targets,
)
from .utils import is_expired_date


class CatalogFilterManager:
    """
    A class responsible for creating and managing catalog filters.

    This class encapsulates the logic for constructing catalog filters from configuration,
    and provides methods for updating and querying filters.
    """

    def __init__(self, config: Targets):
        """
        Initialize the CatalogFilterManager.

        Args:
            config: Configuration to use for constructing catalog filters.
                   If not provided, filters must be set later using set_filters.
        """
        self._catalog_filters = self.construct_filters_from_config(config)
        self._chain_ids = list(self._catalog_filters.keys())
        self._chain_constant_tenors = self.extract_chain_constant_tenors(
            self._catalog_filters
        )

        # Currently for simplicity, the only use case of this item is to store catalog items that have
        # "expiry" and "listing" keys in them. This is so that we can use them to filter out feeds
        # that do not match our listing filters.

        # NOTE: items stored in this map are items that have been processed. It will consider both accepted
        # and rejected items. We do so because we need to store the `original` listing of the expiry and be able
        # to accept/reject items of the stream that will have the same expiry, but will have a different listing date.
        # This is because the listing date referece to the option/futures instrument itself (which includes the strike)
        # and not its expiry
        self._processed_catalog_items: CatalogItemsMap = {}
        # need to acquire the lock whenever we are directly accessing _processed_catalog_items
        self._catalog_lock = asyncio.Lock()

    @staticmethod
    def construct_filters_from_config(config: Targets) -> ChainCatalogFilters:
        """
        Construct catalog filters from configuration.

        Args:
            config: Configuration to use for constructing catalog filters

        Returns:
            A dictionary mapping chain IDs to lists of catalog filters
        """
        chain_filters: ChainCatalogFilters = {}

        for chain_config in config.values():

            if not chain_config.enable:
                continue

            targets = chain_config.targets
            chain_id = chain_config.id
            filters_for_chain = []

            for target in targets:
                constant_tenors = target.constant_tenors
                filters_for_chain.append(
                    FeedIndexerCatalogFilter(
                        exchanges=[
                            exchange.lower() for exchange in target.exchange
                        ],
                        asset_class=target.asset_class,
                        base_assets=[
                            base_asset.upper()
                            for base_asset in target.base_asset
                        ],
                        constant_tenors=constant_tenors,
                        listing_filter=target.listing_filter,
                    )
                )

            chain_filters[chain_id] = filters_for_chain

        return chain_filters

    def get_chain_ids(self) -> list[Network]:
        """
        Get the current catalog filters.

        Returns:
            The current catalog filters
        """
        return self._chain_ids

    def get_filters_for_chain(
        self, chain_id: Network
    ) -> list[FeedIndexerCatalogFilter]:
        """
        Get all filters for a specific chain.

        Args:
            chain_id: The chain ID

        Returns:
            A list of filters for the specified chain
        """
        chain_filters: list[FeedIndexerCatalogFilter] = (
            self._catalog_filters.get(chain_id, [])
        )
        return chain_filters

    def get_constant_tenors_for_chain(
        self,
        chain_id: Network,
    ) -> AssetTypeToCurrencyConstantTenors:
        return self._chain_constant_tenors.get(chain_id, {})

    def feed_matches_base_catalog_filter(
        self,
        feed_string: str,
        chain_id: Network,
        base_feed_metadata: BaseFeedMetaData,
    ) -> bool:
        """
        Check if a feed matches any entry in the ChainCatalogFilters for a given chain.

        Args:
            feed_string: string representation of the Decoded feed
            decoded_feed: The decoded feed object
            chain_id: The chain ID

        Returns:
            True if the feed matches at least one filter, False otherwise
        """
        chain_filters = self.get_filters_for_chain(chain_id)
        if not chain_filters:
            # No filters for this chain, so we can't determine if it matches
            # Default to removing the feeds
            return False

        exchange, base_asset, parent_asset_classes = base_feed_metadata

        if not exchange or not base_asset:
            logging.warning(
                f"Could not extract complete metadata from feed: "
                f"{exchange=}, {base_asset=}, {feed_string=}"
            )
            return True

        for filter in chain_filters:
            if (
                filter.asset_class in parent_asset_classes
                and exchange in filter.exchanges
                and base_asset in filter.base_assets
            ):
                return True

        # If we get here, the feed didn't match any filter
        logging.warning(
            f"Feed does not match any catalog filter for {chain_id=}: "
            f"{exchange=}, {base_asset=}, {feed_string=}"
        )
        return False

    def _accept_catalog_update(self, data: Any, chain_id: Network) -> bool:
        """Determine if a catalog update should be accepted based on filters."""
        if "q" not in data or not data["q"].endswith(".contracts"):
            return False

        if self._is_expired(data):
            return False

        filters_for_chain = self._catalog_filters.get(chain_id, [])
        if not filters_for_chain:
            return False

        if not any(
            filter.accept_catalog_item(data) for filter in filters_for_chain
        ):
            return False

        return True

    async def accept(self, data: Any) -> list[utils_oracle.Network] | None:
        """
        Check if a catalog update should be accepted for any chain.

        Args:
            data: The catalog data to check

        Returns:
            A list of matching chain IDs if the update is accepted, None otherwise
        """
        matching_chain_ids = []
        for chain_id in self.get_chain_ids():
            if self._accept_catalog_update(data, chain_id):
                # if we have already seen this catalog item, then we can skip it
                # we also only perform this check on items with `expiry` and `listing`
                # keys. constant tenor will skip this check
                if "expiry" in data and "listing" in data:
                    catalog_key = self._generate_catalog_key(data)
                    async with self._catalog_lock:
                        if catalog_key in self._processed_catalog_items:
                            continue

                matching_chain_ids.append(chain_id)
                await self._store_catalog_items(catalog_item=data)

        return matching_chain_ids if matching_chain_ids else None

    def extract_chain_constant_tenors(
        self,
        catalog_filters: ChainCatalogFilters,
    ) -> ChainConstantTenorInfo:
        """
        Extract constant tenor configurations for each chain from the provided catalog filters.

        This function iterates over the catalog filters, which map each chain ID to a list of feed
        filters. For each chain, it collects constant tenor values from filters whose asset class is
        either "option" or "future". If multiple filters exist for the same asset type, the resulting
        constant tenor lists are merged with duplicates removed.

        """
        chain_constant_tenors: ChainConstantTenorInfo = {}
        for chain_id, filters in catalog_filters.items():
            asset_to_tenors: AssetTypeToCurrencyConstantTenors = {}
            for f in filters:
                asset_type = f.asset_class
                for base_asset in f.base_assets:

                    if asset_type == "option" or asset_type == "future":
                        tenors = f.constant_tenors or []

                        if asset_type not in asset_to_tenors:
                            asset_to_tenors[asset_type] = {}

                        if base_asset not in asset_to_tenors[asset_type]:
                            asset_to_tenors[asset_type][base_asset] = []

                        asset_to_tenors[asset_type][base_asset] = list(
                            set(
                                asset_to_tenors[asset_type][base_asset] + tenors
                            )
                        )

            chain_constant_tenors[chain_id] = asset_to_tenors
        return chain_constant_tenors

    @staticmethod
    def _is_expired(data: Any) -> bool:
        if "expiry" in data:
            if not utils_general.is_constant_maturity(
                data["expiry"]
            ) and is_expired_date(
                target_date=utils_general.from_iso(data["expiry"]),
                reference_date=datetime.now(tz=UTC),
            ):
                return True
        return False

    async def _get_catalog_entries_from_db(self) -> list[CatalogItem]:
        now = datetime.now(tz=UTC)
        all_instruments = []
        try:
            for _chain_id, chain_filters in self._catalog_filters.items():
                tasks = [
                    get_instruments_async(
                        fields=[],
                        # matches the timeframe we use to expire instruments to ensure we
                        # don't receive errors about not finding catalog keys on cold starts
                        start=utils_general.to_iso(
                            now - EXPIRED_INSTRUMENTS_TIMEDELTA
                        ),
                        end=utils_general.to_iso(now),
                        exchanges=asset_filter.exchanges,
                        asset_types=[asset_filter.asset_class],
                        base_assets=asset_filter.base_assets,
                        quote_assets=["USD"],  # todo: Make this configurable
                    )
                    for asset_filter in chain_filters
                ]
                chain_instruments = [
                    inst for r in await asyncio.gather(*tasks) for inst in r
                ]

                # Process and store instruments for this chain
                all_instruments.extend(
                    await self._process_and_store_chain_instruments(
                        chain_instruments
                    )
                )

        except Exception:
            logging.exception("Error while trying to fetch catalog entries")

        logging.info("Fetched catalog entries for relevant chain filters")

        return all_instruments

    async def _process_and_store_chain_instruments(
        self, instruments: list[CatalogItem]
    ) -> list[CatalogItem]:
        """
        Process instruments for a specific chain and store them in the accepted catalog items dictionary.
        For options with the same expiry, keeps the one with the earliest listing date.

        Args:
            chain_id: The chain ID
            instruments: The list of instruments to process
        """
        seen_option_expiries: dict[str, tuple[datetime, CatalogItem]] = (
            {}
        )  # unique_key -> (earliest_listing_date, catalog_item)
        relevant_instruments = []

        for instr in instruments:
            # option catalog items always refer to the instrument itself not the expiry. We need to ensure
            # that we capture the instrument with the earliest listing date
            if instr["qualified_name"].split(".")[1] == "option":
                unique_key = (
                    f"{instr['qualified_name'].split('.')[0]}_"
                    f"{instr['baseAsset']}_{instr['quoteAsset']}_{instr['expiry']}"
                )

                listing_date = utils_general.from_iso(instr["listing"])

                if unique_key not in seen_option_expiries:
                    seen_option_expiries[unique_key] = (listing_date, instr)
                else:
                    earliest_seen_listing_date = seen_option_expiries[
                        unique_key
                    ][0]
                    if listing_date < earliest_seen_listing_date:
                        seen_option_expiries[unique_key] = (listing_date, instr)

                continue

            relevant_instruments.append(instr)

        for _, (_, catalog_item) in seen_option_expiries.items():
            relevant_instruments.append(catalog_item)

        await self._store_catalog_items(catalog_item=relevant_instruments)

        return relevant_instruments

    async def get_instruments_with_constant_maturity(
        self,
    ) -> list[CatalogItem]:
        """
        Retrieves instruments from the database and adds synthetic constant maturity instruments if provided.
        The constant maturities generates are chain specific
        """
        instruments = await self._get_catalog_entries_from_db()
        for chain_id, chain_filters in self._catalog_filters.items():

            if not chain_filters:
                continue

            logging.info(
                "Generating constant tenor catalogs from filters for chain=%s:",
                chain_id,
            )

            constant_maturity_catalogs = (
                self._generate_constant_maturity_catalogs(
                    chain_filters=chain_filters
                )
            )
            instruments.extend(constant_maturity_catalogs)
        return instruments

    def _generate_constant_maturity_catalogs(
        self,
        chain_filters: list[FeedIndexerCatalogFilter],
    ) -> list[CatalogItem]:
        """
        Generate a synthetic constant maturity catalog instrument based on
        ALL the provided chain_catalog_filter parameters.

        """
        # The same filters may be available across chains. We construct a unique key so make processing faster
        seen_keys = set()
        constant_tenor_catalogs = []
        for filter in chain_filters:
            if "spot" in filter.asset_class:
                # spot instruments dont have expiries
                continue

            exchanges = filter.exchanges
            base_assets = filter.base_assets
            constant_tenors = filter.constant_tenors

            if not constant_tenors:
                continue

            for ex in exchanges:
                for tenor in constant_tenors:
                    # Convert the raw float expiry into the required tenor string
                    for base_asset in base_assets:
                        catalog_key = f"{ex}_{base_asset}_USD_{tenor}"  # todo: make configurable
                        if catalog_key in seen_keys:
                            continue

                        constant_tenor_catalogs.append(
                            CatalogItem(
                                baseAsset=base_asset,
                                qualified_name=f"{ex}.{filter.asset_class}.contracts",
                                quoteAsset="USD",  # todo: make configurable
                                expiry=tenor,
                                # todo: make configurable
                                instrument=f"{base_asset}_USD_{tenor}",  # not directly used
                            )
                        )
                        seen_keys.add(catalog_key)

        return constant_tenor_catalogs

    async def _store_catalog_items(
        self,
        catalog_item: CatalogItem | list[CatalogItem] | Any,
    ) -> None:
        items_to_store = (
            catalog_item if isinstance(catalog_item, list) else [catalog_item]
        )

        async with self._catalog_lock:
            for item in items_to_store:
                # this will safely skip the addition of constant tenor catalog items
                # into the catalog map.
                if "expiry" in item and "listing" in item:
                    # Store the accepted catalog item for this specific chain
                    catalog_key = self._generate_catalog_key(item)
                    if catalog_key not in self._processed_catalog_items:
                        self._processed_catalog_items[catalog_key] = item

    @staticmethod
    def _generate_catalog_key(catalog_item: CatalogItem | Any) -> str:
        """
        Generate a unique key for a catalog item to use in the accepted catalog items dictionary.

        Args:
            catalog_item: The catalog item to generate a key for

        Returns:
            A unique string key for the catalog item
        """

        qualified_name = str(
            catalog_item.get("qualified_name", catalog_item.get("q", ""))
        )
        qn_tokens = qualified_name.split(".")

        exchange = qn_tokens[0]
        asset_class = qn_tokens[1]
        base_asset = catalog_item.get("baseAsset", "")
        # todo: make configurable when multiple quote assets are supported
        quote_asset = "USD"
        expiry = catalog_item.get("expiry", "")

        if expiry and len(expiry) > 20:
            expiry = f"{expiry[:19]}Z"

        return f"{exchange}_{asset_class}_{base_asset}_{quote_asset}_{expiry}"

    async def get_processed_catalog_items(
        self,
    ) -> CatalogItemsMap:
        """
        Get the dictionary of catalog items for a specific chain.
        """
        async with self._catalog_lock:
            return copy.deepcopy(self._processed_catalog_items)

    async def cleanup_expired_catalog_items(self) -> int:
        """
        Clean up expired catalog items from the _processed_catalog_items dictionary.

        Uses the same reference timestamp logic as FeedCleanupManager._is_feed_expired
        to determine expiry.

        Returns:
            The number of expired catalog items removed
        """

        ref_timestamp = datetime.now(tz=UTC) - EXPIRED_INSTRUMENTS_TIMEDELTA

        items_removed = 0

        async with self._catalog_lock:
            keys_to_remove = []
            # access state directly since we have acquired the lock and need to write
            for catalog_key, item in list(
                self._processed_catalog_items.items()
            ):
                if "expiry" in item and not utils_general.is_constant_maturity(
                    item["expiry"]
                ):
                    expiry_date = utils_general.from_iso(item["expiry"])
                    if is_expired_date(
                        reference_date=ref_timestamp,
                        target_date=expiry_date,
                    ):
                        keys_to_remove.append(catalog_key)

                # Remove expired items
                for key in keys_to_remove:
                    del self._processed_catalog_items[key]
                    items_removed += 1

        if items_removed > 0:
            logging.info(f"Cleaned up {items_removed} expired catalog items")

        return items_removed
