from datetime import UTC, datetime, timedelta
from typing import Any

import pytest
from datagrabber import CatalogItem
from utils_oracle import Feed, Network

from feed_indexer.catalog_filter_manager import CatalogFilterManager
from feed_indexer.typings import (
    BaseFeedMetaData,
    ChainCatalogFilters,
    ChainConstantTenorInfo,
    FeedIndexerCatalogFilter,
    Targets,
)


@pytest.mark.asyncio
class TestCatalogFilterManager:
    @pytest.mark.parametrize(
        "feed_string, base_feed_meta, expected",
        [
            # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000041dc7bf51b7c6000", "enumerable": [4, 0, 2, 2]}}',
                ("bybit", "ETH", ["option"]),
                False,  # exchange doesnt match
            ),
            # spot_ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_
            (
                '{"id": 3, "parameters": {"other": "0x", "enumerable": [1, 1]}}',
                ("deribit", "BTC", ["option", "spot"]),
                True,  # options depend on spot
            ),
            # future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:720m
            (
                '{"id": 1, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000000000014e707", "enumerable": [1, 1, 1]}}',
                ("deribit", "BTC", ["option", "future"]),
                True,  # constant tenor not checked as part of _matches_catalog_filter
            ),
            # "future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:30d" - contains constant tenor rounding which the FI should
            (
                '{"id":1,"parameters":{"other":"0x0000000000000000000000000000000000000000000000000000000004e625a4","enumerable":[1,1,1]}}',
                ("deribit", "BTC", ["option", "future"]),
                True,
            ),
        ],
    )
    def test_feed_matches_base_catalog_filter_options_filter(
        self,
        feed_string: str,
        base_feed_meta: BaseFeedMetaData,
        expected: bool,
        catalog_filter_manager: CatalogFilterManager,
    ) -> None:
        """Test _matches_catalog_filter with options-only catalog filter."""
        catalog_filters = {
            Network(1): [
                FeedIndexerCatalogFilter(
                    exchanges=["deribit"],
                    asset_class="option",
                    base_assets=["ETH", "BTC"],
                    constant_tenors=["7d", "14d"],
                )
            ]
        }

        catalog_filter_manager._catalog_filters = catalog_filters

        feed = Feed.model_validate_json(feed_string)
        decoded_feed = feed.get_decoded_feed()
        result = catalog_filter_manager.feed_matches_base_catalog_filter(
            feed_string=decoded_feed.feed_to_string(chain_decimals=9),
            chain_id=Network(1),
            base_feed_metadata=base_feed_meta,
        )
        assert result == expected

    @pytest.mark.parametrize(
        "feed_string, base_feed_meta, expected",
        [
            # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000041dc7bf51b7c6000", "enumerable": [4, 0, 2, 2]}}',
                ("bybit", "ETH", ["option"]),
                False,  # params not required when futures are specified
            ),
            # spot_ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_
            (
                '{"id": 3, "parameters": {"other": "0x", "enumerable": [1, 1]}}',
                ("deribit", "BTC", ["option", "spot"]),
                False,  # mismatching exchange
            ),
            # future_ExpiryTypeEnum:TENOR+ExchangeEnum:BYBIT+BaseAssetEnum:BTC_expiry:720m
            (
                '{"id": 1, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000000000014e707", "enumerable": [1, 2, 1]}}',
                ("bybit", "BTC", ["option", "future"]),
                True,  # constant tenors not checked
            ),
            # "future_ExpiryTypeEnum:TENOR+ExchangeEnum:BYBIT+BaseAssetEnum:BTC_expiry:30d"
            (
                '{"id":1,"parameters":{"other":"0x0000000000000000000000000000000000000000000000000000000004e625a4","enumerable":[1,2,1]}}',
                ("bybit", "BTC", ["option", "future"]),
                True,  # bybit futures accepted
            ),
        ],
    )
    def test_feed_matches_base_catalog_filter_futures_filter(
        self,
        expected: bool,
        feed_string: str,
        base_feed_meta: BaseFeedMetaData,
        catalog_filter_manager: CatalogFilterManager,
    ) -> None:
        """Test _matches_catalog_filter with futures-only catalog filter."""
        catalog_filters = {
            Network(1): [
                FeedIndexerCatalogFilter(
                    exchanges=["bybit"],
                    asset_class="future",
                    base_assets=["ETH", "BTC"],
                    constant_tenors=["7d", "14d"],
                )
            ]
        }

        catalog_filter_manager._catalog_filters = catalog_filters

        feed = Feed.model_validate_json(feed_string)
        decoded_feed = feed.get_decoded_feed()
        result = catalog_filter_manager.feed_matches_base_catalog_filter(
            feed_string=decoded_feed.feed_to_string(chain_decimals=9),
            chain_id=Network(1),
            base_feed_metadata=base_feed_meta,
        )
        assert result == expected

    @pytest.mark.parametrize(
        "feed_string, base_feed_meta ,expected",
        [
            # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000041dc7bf51b7c6000", "enumerable": [4, 0, 2, 2]}}',
                ("bybit", "ETH", ["option"]),
                False,
            ),
            # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:1970-01-01T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000016eb550c6000", "enumerable": [4, 0, 2, 2]}}',
                ("bybit", "ETH", ["option"]),
                False,
            ),
            # spot_ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_
            (
                '{"id": 3, "parameters": {"other": "0x", "enumerable": [1, 1]}}',
                ("deribit", "BTC", ["option", "spot"]),
                True,  # spot accepted
            ),
            # future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:720m
            (
                '{"id": 1, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000000000014e707", "enumerable": [1, 1, 1]}}',
                ("deribit", "BTC", ["option", "future"]),
                False,
            ),
            # "params_SVIParamEnum:SVI_RHO+ExpiryTypeEnum:TENOR+ExchangeEnum:BYBIT+BaseAssetEnum:BTC_expiry:480m"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000000000000000def5a", "enumerable": [2, 1, 2, 1]}}',
                ("bybit", "BTC", ["option"]),
                False,
            ),
            # "future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:30d" - contains constant tenor rounding which the FI should
            (
                '{"id":1,"parameters":{"other":"0x0000000000000000000000000000000000000000000000000000000004e625a4","enumerable":[1,1,1]}}',
                ("deribit", "BTC", ["option", "future"]),
                False,
            ),
        ],
    )
    def test_feed_matches_base_catalog_filter_spot_filter(
        self,
        expected: bool,
        feed_string: str,
        base_feed_meta: BaseFeedMetaData,
        catalog_filter_manager: CatalogFilterManager,
    ) -> None:
        """Test _matches_catalog_filter with spot-only catalog filter."""
        catalog_filters = {
            Network(1): [
                FeedIndexerCatalogFilter(
                    exchanges=["bybit", "deribit"],
                    asset_class="spot",
                    base_assets=["ETH", "BTC"],
                    constant_tenors=[],
                )
            ]
        }

        catalog_filter_manager._catalog_filters = catalog_filters

        feed = Feed.model_validate_json(feed_string)
        decoded_feed = feed.get_decoded_feed()
        result = catalog_filter_manager.feed_matches_base_catalog_filter(
            feed_string=decoded_feed.feed_to_string(chain_decimals=9),
            chain_id=Network(1),
            base_feed_metadata=base_feed_meta,
        )
        assert result == expected

    def test_construct_filters_from_config(
        self, mock_target_config: Targets
    ) -> None:
        """Test constructing filters from configuration."""
        filters = CatalogFilterManager.construct_filters_from_config(
            mock_target_config
        )

        from utils_oracle import Network

        assert len(filters) == 3

        for chain_id, chain_filters in filters.items():
            assert isinstance(chain_id, Network)
            assert isinstance(chain_filters, list)

            for filter_obj in chain_filters:

                assert hasattr(filter_obj, "exchanges")
                assert hasattr(filter_obj, "asset_class")
                assert hasattr(filter_obj, "base_assets")
                assert hasattr(filter_obj, "constant_tenors")

    @pytest.mark.parametrize(
        "chain_id, expected_filters",
        [(1, True), (2, True), (3, True)],
    )
    def test_get_filters_for_chain(
        self,
        catalog_filter_manager: CatalogFilterManager,
        chain_id: Network,
        expected_filters: bool,
    ) -> None:
        """Test getting filters for a specific chain."""
        chain_filters = catalog_filter_manager.get_filters_for_chain(chain_id)
        assert isinstance(chain_filters, list)

        if expected_filters:
            assert len(chain_filters) > 0
        else:
            assert len(chain_filters) == 0

    @pytest.mark.parametrize(
        "catalog_data",
        [
            # expired
            {
                "q": "deribit.option.contracts",
                "baseAsset": "BTC",
                "expiry": "2024-01-01T08:00:00Z",
            },
            # tenor not in filter
            {
                "q": "bybit.option.contracts",
                "baseAsset": "BTC",
                "expiry": "64m",
            },
            # Base asset not in filters
            {
                "q": "blockscholes.option.contracts",
                "baseAsset": "XRP",
                "expiry": "480m",
            },
            # Exchange not in filters
            {
                "q": "unknown_exchange.option.contracts",
                "baseAsset": "BTC",
                "expiry": "14d",
            },
            # Asset class not in filters
            {
                "q": "deribit.unknown_asset.contracts",
                "baseAsset": "BTC",
                "expiry": "30d",
            },
            # not a catalog qn
            {
                "q": "deribit.unknown_asset.something",
                "baseAsset": "BTC",
                "expiry": "30d",
            },
            # incorrect qn key - should be 'q'
            {
                "qualified_name": "deribit.option.contracts",
                "baseAsset": "BTC",
                "expiry": "30d",
            },
            {
                "q": "bybit.spot.contracts",
                "baseAsset": "BTC",
            },
        ],
    )
    def test_accept_catalog_update_false(
        self,
        catalog_filter_manager: CatalogFilterManager,
        catalog_data: dict[str, Any],
    ) -> None:
        """Test the accept_catalog_update method."""
        for chain_id in catalog_filter_manager.get_chain_ids():
            result = catalog_filter_manager._accept_catalog_update(
                data=catalog_data, chain_id=chain_id
            )

            assert result == False

    @pytest.mark.parametrize(
        "catalog_data",
        [
            # Valid option with known exchange, asset class, base asset, and tenor
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                }
            ),
            # Valid future with known exchange, asset class, base asset
            (
                {
                    "q": "blockscholes.future.contracts",
                    "baseAsset": "ETH",
                    "expiry": "2028-01-01T08:00:00Z",
                }
            ),
        ],
    )
    def test_accept_catalog_update_true(
        self,
        catalog_filter_manager: CatalogFilterManager,
        catalog_data: dict[str, Any],
    ) -> None:
        """Test the accept_catalog_update method with cases expected to pass."""
        for chain_id in catalog_filter_manager.get_chain_ids():
            result = catalog_filter_manager._accept_catalog_update(
                data=catalog_data, chain_id=chain_id
            )

            assert result == True

    @pytest.mark.parametrize(
        "catalog_data",
        [
            # Valid option with known exchange, asset class, base asset, and tenor
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                    "listing": "2023-06-27T08:00:00Z",
                }
            ),
            # Valid future with known exchange, asset class, base asset
            (
                {
                    "q": "blockscholes.future.contracts",
                    "baseAsset": "ETH",
                    "expiry": "2028-01-01T08:00:00Z",
                    "listing": "2023-01-01T08:00:00Z",
                }
            ),
        ],
    )
    async def test_accept_catalog_update_true_and_storage(
        self,
        catalog_filter_manager: CatalogFilterManager,
        catalog_data: dict[str, Any],
    ) -> None:
        """Test the accept_catalog_update method with cases expected to pass and verify storage."""
        # Clear any existing items
        catalog_filter_manager._processed_catalog_items = {}

        # Test accept method
        matching_chain_ids = await catalog_filter_manager.accept(catalog_data)
        assert matching_chain_ids is not None
        assert len(matching_chain_ids) > 0

        # Verify that the catalog item was stored
        catalog_key = catalog_filter_manager._generate_catalog_key(catalog_data)
        chain_items = await catalog_filter_manager.get_processed_catalog_items()
        assert catalog_key in chain_items
        assert chain_items[catalog_key] == catalog_data

    async def test_accept_duplicate_catalog_update(
        self,
        catalog_filter_manager: CatalogFilterManager,
    ) -> None:
        """Test the accept_catalog_update method with duplicate catalog items."""

        catalog_filter_manager._processed_catalog_items = {}

        catalog_data = {
            "q": "deribit.option.contracts",
            "baseAsset": "BTC",
            "expiry": "2120-05-22T08:00:00Z",
            "listing": "2023-06-27T08:00:00Z",
        }
        matching_chain_ids = await catalog_filter_manager.accept(catalog_data)
        assert matching_chain_ids is not None
        assert len(matching_chain_ids) > 0

        # Verify that the catalog item was stored for each matching chain
        catalog_key = catalog_filter_manager._generate_catalog_key(catalog_data)
        chain_items = await catalog_filter_manager.get_processed_catalog_items()
        assert catalog_key in chain_items
        assert chain_items[catalog_key] == catalog_data

        # Test accept method again with same data
        matching_chain_ids = await catalog_filter_manager.accept(catalog_data)
        assert matching_chain_ids is None

    def test_extract_chain_constant_tenors(
        self,
        mock_all_chain_catalog_filter: ChainCatalogFilters,
        catalog_filter_manager: CatalogFilterManager,
    ) -> None:
        from utils_oracle import Network

        result = catalog_filter_manager.extract_chain_constant_tenors(
            mock_all_chain_catalog_filter
        )
        expected = {
            Network(1): {
                "option": {
                    "BTC": sorted(["480m", "240m", "720m"]),
                    "ETH": sorted(["480m", "240m", "720m"]),
                }
            },
            Network(2): {
                "option": {
                    "BTC": sorted(["480m", "240m", "720m"]),
                    "ETH": sorted(["480m", "240m", "720m"]),
                }
            },
            Network(3): {
                "option": {
                    "BTC": sorted(["480m", "240m", "720m"]),
                    "ETH": sorted(["480m", "240m", "720m"]),
                }
            },
        }

        # sort resulting list for comparison
        modified_result: ChainConstantTenorInfo = {}
        for network, asset_classes in result.items():
            modified_result[network] = {}
            for asset_class, assets in asset_classes.items():
                modified_result[network][asset_class] = {}
                for asset, tenors in assets.items():
                    modified_result[network][asset_class][asset] = sorted(
                        tenors
                    )

        assert modified_result == expected

    @pytest.mark.parametrize(
        "test_case, expected",
        [
            # Test case 1: No expiry field - should not be considered expired
            (
                {
                    "q": "deribit.spot.contracts",
                    "baseAsset": "BTC",
                },
                False,
            ),
            # Test case 2: Constant maturity tenor - should not be considered expired
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                },
                False,
            ),
            # Test case 3: Future ISO date - should not be considered expired
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": (
                        datetime.now(tz=UTC) + timedelta(days=30)
                    ).isoformat(),
                },
                False,
            ),
            # Test case 4: Past ISO date - should be considered expired
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": (
                        datetime.now(tz=UTC) - timedelta(days=30)
                    ).isoformat(),
                },
                True,
            ),
            # Test case 5: Almost expired ISO date (1 minute in the future)
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": (
                        datetime.now(tz=UTC) + timedelta(minutes=1)
                    ).isoformat(),
                },
                False,
            ),
            # Test case 6: Just expired ISO date (1 minute in the past)
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": (
                        datetime.now(tz=UTC) - timedelta(minutes=1)
                    ).isoformat(),
                },
                True,
            ),
        ],
    )
    def test_is_expired(
        self, test_case: dict[str, Any], expected: bool
    ) -> None:
        """Test the _is_expired method using parametrized test cases."""

        result = CatalogFilterManager._is_expired(test_case)

        assert result == expected

    @pytest.mark.parametrize(
        "chain_to_constant_tenors, chain_to_exchanges, expected",
        [
            (
                {1: ["1d", "2d", "7d"]},
                {1: ["blockscholes"]},
                [
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "2d",
                        "instrument": "BTC_USD_2d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "7d",
                        "instrument": "BTC_USD_7d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                ],
            ),
            (
                {1: []},
                {1: ["blockscholes"]},
                [],  # no expected constant tenors
            ),
            (
                {1: ["1d"]},
                {1: ["blockscholes", "deribit"]},
                [
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "deribit.option.contracts",
                        "quoteAsset": "USD",
                    },
                ],
            ),
            (
                {1: ["1d"], 2: ["2d"]},
                {1: ["blockscholes"], 2: ["deribit"]},
                [
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "2d",
                        "instrument": "BTC_USD_2d",
                        "qualified_name": "deribit.option.contracts",
                        "quoteAsset": "USD",
                    },
                ],
            ),
            (
                {1: ["1d"], 2: ["2d"]},
                {
                    1: ["blockscholes", "deribit"],
                    2: ["deribit", "blockscholes"],
                },
                [
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "1d",
                        "instrument": "BTC_USD_1d",
                        "qualified_name": "deribit.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "2d",
                        "instrument": "BTC_USD_2d",
                        "qualified_name": "blockscholes.option.contracts",
                        "quoteAsset": "USD",
                    },
                    {
                        "baseAsset": "BTC",
                        "expiry": "2d",
                        "instrument": "BTC_USD_2d",
                        "qualified_name": "deribit.option.contracts",
                        "quoteAsset": "USD",
                    },
                ],
            ),
        ],
    )
    async def test_get_instruments_with_constant_maturity(
        self,
        mock_all_chain_catalog_filter: ChainCatalogFilters,
        chain_to_constant_tenors: dict[Network, list[str]],
        chain_to_exchanges: dict[Network, list[str]],
        catalog_filter_manager: CatalogFilterManager,
        expected: dict[str, Any],
    ) -> None:
        # Setup chain catalog filters
        for chain in mock_all_chain_catalog_filter.copy().keys():
            if chain.value not in chain_to_exchanges:
                # deleting for cleaner setup of mock_all_chain_catalog_filter for test
                # keep only the ones relevant to the test
                del mock_all_chain_catalog_filter[chain]
                continue

            mock_all_chain_catalog_filter[chain] = [
                FeedIndexerCatalogFilter(
                    exchanges=chain_to_exchanges[chain],
                    asset_class="option",
                    base_assets=["BTC"],
                    constant_tenors=chain_to_constant_tenors[chain],
                )
            ]

        catalog_filter_manager._catalog_filters = mock_all_chain_catalog_filter

        result = (
            await catalog_filter_manager.get_instruments_with_constant_maturity()
        )

        assert sorted(
            result, key=lambda d: (d["instrument"], d["qualified_name"])
        ) == sorted(
            expected, key=lambda d: (d["instrument"], d["qualified_name"])  # type: ignore
        )

    def test_generate_constant_maturity_catalogs(
        self,
        catalog_filter_manager: CatalogFilterManager,
        mock_all_chain_catalog_filter: ChainCatalogFilters,
    ) -> None:

        for _chain, filters in mock_all_chain_catalog_filter.items():
            catalogs = (
                catalog_filter_manager._generate_constant_maturity_catalogs(
                    chain_filters=filters
                )
            )
            assert catalogs == [
                {
                    "baseAsset": "BTC",
                    "expiry": "240m",
                    "instrument": "BTC_USD_240m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "240m",
                    "instrument": "ETH_USD_240m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "480m",
                    "instrument": "BTC_USD_480m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "480m",
                    "instrument": "ETH_USD_480m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "720m",
                    "instrument": "BTC_USD_720m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "720m",
                    "instrument": "ETH_USD_720m",
                    "qualified_name": "deribit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "240m",
                    "instrument": "BTC_USD_240m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "240m",
                    "instrument": "ETH_USD_240m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "480m",
                    "instrument": "BTC_USD_480m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "480m",
                    "instrument": "ETH_USD_480m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "720m",
                    "instrument": "BTC_USD_720m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "720m",
                    "instrument": "ETH_USD_720m",
                    "qualified_name": "bybit.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "240m",
                    "instrument": "BTC_USD_240m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "240m",
                    "instrument": "ETH_USD_240m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "480m",
                    "instrument": "BTC_USD_480m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "480m",
                    "instrument": "ETH_USD_480m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "720m",
                    "instrument": "BTC_USD_720m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
                {
                    "baseAsset": "ETH",
                    "expiry": "720m",
                    "instrument": "ETH_USD_720m",
                    "qualified_name": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                },
            ]

    def test_generate_constant_maturity_catalogs_single_exchange_single_currency(
        self,
        catalog_filter_manager: CatalogFilterManager,
        mock_all_chain_catalog_filter: ChainCatalogFilters,
        mock_constant_tenors: list[str],
    ) -> None:
        filter = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            asset_class="option",
            base_assets=["BTC"],
            constant_tenors=mock_constant_tenors,
        )

        catalogs = catalog_filter_manager._generate_constant_maturity_catalogs(
            chain_filters=[filter]
        )
        assert catalogs == [
            {
                "baseAsset": "BTC",
                "expiry": "240m",
                "instrument": "BTC_USD_240m",
                "qualified_name": "deribit.option.contracts",
                "quoteAsset": "USD",
            },
            {
                "baseAsset": "BTC",
                "expiry": "480m",
                "instrument": "BTC_USD_480m",
                "qualified_name": "deribit.option.contracts",
                "quoteAsset": "USD",
            },
            {
                "baseAsset": "BTC",
                "expiry": "720m",
                "instrument": "BTC_USD_720m",
                "qualified_name": "deribit.option.contracts",
                "quoteAsset": "USD",
            },
        ]

    @pytest.mark.parametrize(
        "catalog_item, expected",
        [
            # Test with qualified_name (DB format)
            (
                {
                    "qualified_name": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "2025-06-27T08:00:00Z",
                },
                "deribit_option_BTC_USD_2025-06-27T08:00:00Z",
            ),
            # Test with q (stream format)
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "2025-06-27T08:00:00Z",
                },
                "deribit_option_BTC_USD_2025-06-27T08:00:00Z",
            ),
            # Test with missing fields
            (
                {
                    "q": "deribit.spot.contracts",
                    "baseAsset": "BTC",
                },
                "deribit_spot_BTC_USD_",
            ),
            # Test quote asset defaults to USD (DB format)
            (
                {
                    "qualified_name": "bybit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USDT",
                    "expiry": "2025-06-27T08:00:00Z",
                },
                "bybit_option_BTC_USD_2025-06-27T08:00:00Z",
            ),
            # Test expiry is truncated to seconds
            (
                {
                    "qualified_name": "bybit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "2025-06-27T08:00:00.000Z",
                },
                "bybit_option_BTC_USD_2025-06-27T08:00:00Z",
            ),
        ],
    )
    def test_generate_catalog_key(
        self,
        catalog_filter_manager: CatalogFilterManager,
        catalog_item: CatalogItem,
        expected: str,
    ) -> None:
        """Test the _generate_catalog_key method with different catalog item formats."""
        db_key = catalog_filter_manager._generate_catalog_key(catalog_item)
        assert db_key == expected

    async def test_process_and_store_chain_instruments(
        self, catalog_filter_manager: CatalogFilterManager
    ) -> None:
        """Test the _process_and_store_chain_instruments method."""

        instruments: list[CatalogItem] = [
            {
                "qualified_name": "deribit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
                "expiry": "2025-06-27T08:00:00Z",
                "listing": "2023-06-27T08:00:00Z",
                "type": "C",
                "strike": 100000,
            },
            {
                "qualified_name": "deribit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
                "expiry": "2025-06-27T08:00:00Z",  # Same expiry as above
                "listing": "2023-05-15T08:00:00Z",  # Earlier listing date - this should be kept
                "type": "P",
                "strike": 90000,
            },
            {
                "qualified_name": "deribit.future.contracts",
                "baseAsset": "ETH",
                "quoteAsset": "USD",
                "expiry": "2025-12-27T08:00:00Z",
                "listing": "2023-12-27T08:00:00Z",
            },
            {
                "qualified_name": "bybit.spot.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
            },
        ]

        # Process and store the instruments
        await catalog_filter_manager._process_and_store_chain_instruments(
            instruments
        )

        catalog_items = (
            await catalog_filter_manager.get_processed_catalog_items()
        )
        # Should have 2 items (option duplicates are filtered as well as those without both the expiry and listing fields)
        assert len(catalog_items) == 2

        # Check that the option duplicate was filtered out
        option_keys = [k for k in catalog_items.keys() if "option" in k]
        assert len(option_keys) == 1

        option_key = option_keys[0]
        stored_option = catalog_items[option_key]

        assert stored_option["listing"] == "2023-05-15T08:00:00Z"
        assert stored_option["type"] == "P"
        assert stored_option["strike"] == 90_000

        # Check that the future was stored
        future_key = catalog_filter_manager._generate_catalog_key(
            instruments[2]
        )
        assert future_key in catalog_items
        assert catalog_items[future_key] == instruments[2]

    @pytest.mark.parametrize(
        "item, should_be_stored",
        [
            (
                # "item with expiry and listing",
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "2023-06-27T08:00:00Z",
                    "listing": "2023-06-27T08:00:00Z",
                },
                True,
            ),
            (
                # "item missing expiry",
                {
                    "q": "deribit.spot.contracts",
                    "baseAsset": "BTC",
                    "listing": "2023-06-27T08:00:00Z",
                },
                False,
            ),
            (
                # "item missing listing",
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                },
                False,
            ),
        ],
    )
    async def test_store_catalog_item(
        self,
        catalog_filter_manager: CatalogFilterManager,
        item: CatalogItem,
        should_be_stored: bool,
    ) -> None:
        """Test the _store_catalog_item method."""
        # Clear any existing items
        catalog_filter_manager._processed_catalog_items = {}
        await catalog_filter_manager._store_catalog_items(item)
        catalog_key = catalog_filter_manager._generate_catalog_key(item)

        if should_be_stored:
            assert (
                catalog_key in catalog_filter_manager._processed_catalog_items
            )
        else:
            assert (
                catalog_key
                not in catalog_filter_manager._processed_catalog_items
            )

    @pytest.mark.parametrize(
        "items, expected_stored_count",
        [
            (
                # "list with valid and invalid items",
                [
                    {
                        "q": "deribit.option.contracts",
                        "baseAsset": "BTC",
                        "expiry": "2023-06-27T08:00:00Z",
                        "listing": "2023-06-27T08:00:00Z",
                    },
                    {
                        "q": "deribit.spot.contracts",
                        "baseAsset": "BTC",
                        "listing": "2023-06-27T08:00:00Z",
                    },
                    {
                        "q": "deribit.option.contracts",
                        "baseAsset": "ETH",
                        "expiry": "2023-07-01T08:00:00Z",
                        "listing": "2023-07-01T08:00:00Z",
                    },
                ],
                2,  # Only 2 items should be stored (missing expiry in second item)
            ),
            (
                # "empty list",
                [],
                0,
            ),
            (
                # "list with all valid items",
                [
                    {
                        "q": "deribit.option.contracts",
                        "baseAsset": "BTC",
                        "expiry": "2023-06-27T08:00:00Z",
                        "listing": "2023-06-27T08:00:00Z",
                    },
                    {
                        "q": "deribit.future.contracts",
                        "baseAsset": "ETH",
                        "expiry": "2023-07-01T08:00:00Z",
                        "listing": "2023-07-01T08:00:00Z",
                    },
                ],
                2,
            ),
        ],
    )
    async def test_store_catalog_items_list(
        self,
        catalog_filter_manager: CatalogFilterManager,
        items: list[CatalogItem],
        expected_stored_count: int,
    ) -> None:
        """Test the _store_catalog_items method with a list of items."""
        catalog_filter_manager._processed_catalog_items = {}
        await catalog_filter_manager._store_catalog_items(items)

        stored_count = len(catalog_filter_manager._processed_catalog_items)
        assert stored_count == expected_stored_count
